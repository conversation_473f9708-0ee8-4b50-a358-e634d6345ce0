const bmsxService = require('../../services/bmsx/bmsx.service');

/**
 * Discovery Result - Receive discovery result from IoT Core with S3 bucket location
 */
module.exports = {
  friendlyName: 'Discovery Result',
  description: 'Receive discovery result from IoT Core with S3 bucket location',

  inputs: {
    siteId: {
      type: 'string',
      required: true,
      description: 'Site ID'
    },

    slaveControllerId: {
      type: 'string',
      required: true,
      description: 'Slave controller ID (deviceId in DynamoDB)'
    },

    protocol: {
      type: 'string',
      required: true,
      description: 'Protocol used (e.g., bacnet_to_mqtt)'
    },

    connector: {
      type: 'string',
      required: true,
      description: 'Connector class (e.g., n3uronbacnetmqtt)'
    },

    operation: {
      type: 'string',
      required: true,
      description: 'Operation type (e.g., listConnectorTags)'
    },

    requestId: {
      type: 'string',
      required: true,
      description: 'Request ID from original discovery request'
    },

    status: {
      type: 'number',
      required: true,
      description: 'Status code (1 = success, 0 = failed)'
    },

    s3Bucket: {
      type: 'string',
      required: false,
      description: 'S3 bucket name (required when status = 1)'
    },

    s3Object: {
      type: 'string',
      required: false,
      description: 'S3 object path (required when status = 1)'
    },

    ts: {
      type: 'string',
      required: true,
      description: 'Timestamp in ISO format with timezone'
    }
  },

  exits: {
    success: {
      statusCode: 200,
    },
    badRequest: {
      statusCode: 400,
    },
    serverError: {
      responseType: "serverError",
      statusCode: 500,
    },
  },

  fn: async function (inputs, exits) {
    const { siteId, slaveControllerId, requestId, status, s3Bucket, s3Object } = inputs;

    try {
      // Validate required fields for successful status
      if (status === 1 && (!s3Bucket || !s3Object)) {
        return exits.badRequest({
          message: 'S3 bucket and object are required when status is success (1)'
        });
      }

      const result = await bmsxService.processDiscoveryResult({
        siteId,
        slaveControllerId,
        requestId,
        status,
        s3Bucket,
        s3Object
      });

      return exits.success({
        message: result.message
      });

    } catch (error) {
      sails.log.error('[BMS > discovery-result] Error:', error);

      if (error.code === 'E_BAD_REQUEST') {
        return exits.badRequest({ message: error.message });
      }

      return exits.serverError({
        message: 'Failed to process discovery result',
        error: error.message
      });
    }
  }
};
