const deviceService = require("../../services/device/device.service");
const deviceUtils = require("../../utils/device/add-device.utils");
const parameterService = require("../../services/parameter/parameter.service");
const deviceTypeService = require("../../services/devicetype/devicetype.service");
const auditEventLogService = require("../../services/auditEventLog/auditEventLog.public");
const parameterservice = require("../../services/parameter/parameter.public");
const utils = require("../../utils/device/utils");
const devicetypeservice = require("../../services/devicetype/devicetype.public");
const dynamokeystoreservice = require("../../services/dynamokeystore/dynamokeystore.public");

module.exports = {
  friendlyName: "Fetch master controller devices",
  description: "fetch bacnet devices as third party master controllers.",

  inputs: {
    _userMeta: {
      type: "json",
      required: false,
      example: { id: "userId", _role: "role", _site: "siteId" },
    },
    siteId: {
      type: "string",
      required: true,
      description: "Site id.",
    },
    masterControllerId: {
      type: "number",
      required: true,
      description: "ID of the master controller.",
    },
    bacnetSlaveControllerId: {
      type: "number",
      required: true,
      description: "ID of the BACnet slave controller.",
    },
    operations: {
      type: "json",
      required: false,
      description:
        "Array of operations to perform (INSERT, UPDATE, DELETE) with their respective data.",
      example: [
        {
          type: "INSERT",
          data: [
            {
              controllerId: "36335",
              driverType: "-999",
              deviceType: "relayFeedback",
              name: "Test Relay Feedback - KIMS",
              address: "",
              data: {},
              command: {},
              siteId: "sjo-del",
            },
          ],
        },
        {
          type: "UPDATE",
          data: [
            {
              id: 42,
              controllerId: "36335",
              driverType: "-999",
              deviceType: "relayFeedback",
              deviceId: 32456,
              name: "Test Relay Feedback - KIMS",
              address: "",
              data: {},
              command: {},
              siteId: "sjo-del",
            },
          ],
        },
      ],
    },
  },

  exits: {
    success: {
      description: "Success",
    },
    serverError: {
      statusCode: 500,
      description: "Server error occurred",
    },
    notFound: {
      statusCode: 404,
      description: "Devices not found.",
    },
    badRequest: {
      statusCode: 400,
      description: "Bad request",
    },
  },

  fn: async function (inputs, exits) {
    try {
      const { siteId, masterControllerId, bacnetSlaveControllerId, operations } = inputs;

      const successDevices = [];
      const errorDevices = [];
      const masterControllerData = await Devices.findOne({
        // deviceId: "3635",
        deviceId: masterControllerId,
        siteId,
      });

      //neh - return if master controller is not found
      // if (!masterControllerData) {
      //   return exits.notFound({ message: "Master controller not found." });
      // }

      for (const operation of operations) {
        const { type, data } = operation;

        if (type === "INSERT") {
          for (const deviceInfo of data) {
            deviceInfo.siteId = siteId;
            errorDevices.push(deviceInfo);

            let isMainMeter = false;
            let isEnergyMeter = false;

            if (deviceInfo.deviceType === "em") {
              isEnergyMeter = true;
            }

            if (deviceInfo.hasOwnProperty("name")) {
              deviceInfo.name = deviceInfo.name.trim();
            }

            const deviceType = deviceInfo.deviceType;
            const driverType = deviceInfo.driverType;

            // if the controllerId of the device does not exist and value is not belong the Mater BMSX. if the value is not n3uronbacnetmqtt then stop the operation
            const connectedControllerDetail = await Devices.findOne({
              deviceId: deviceInfo.controllerId,
              siteId,
            });

            if (!connectedControllerDetail) {
              // If the connectedControllerDetail is not found, exit from this loop and add error message to errorControllers
              const tempDeviceId = deviceInfo.tempDeviceId;
              const errorIndex = errorDevices.findIndex(
                (d) => d.tempDeviceId && d.tempDeviceId === tempDeviceId
              );
              if (errorIndex !== -1) {
                errorDevices[errorIndex].message =
                  "Connected controller not found for the given controllerId.";
              }
              continue;
            }

            if (
              connectedControllerDetail.hasOwnProperty("vendorId") &&
              connectedControllerDetail.vendorId !== "n3uronbacnetmqtt"
            ) {
              const tempDeviceId = deviceInfo.tempDeviceId;
              const errorIndex = errorDevices.findIndex(
                (d) => d.tempDeviceId && d.tempDeviceId === tempDeviceId
              );
              if (errorIndex !== -1) {
                errorDevices[errorIndex].message =
                  "Connected controller vendorId is not n3uronbacnetmqtt.";
              }
              continue;
            }

            deviceInfo.communicationType = connectedControllerDetail.vendorId;

            const deviceDriver = await deviceTypeService.findOne({ deviceType, driverType });

            //TODO: add check for deviceDriver
            const functionType = deviceUtils.getFunctionType(deviceDriver);
            deviceInfo.functionType = functionType;
            // Only add mbBatchReadEnable if it's not null or undefined
            if (
              deviceDriver.mbBatchReadEnable !== undefined &&
              deviceDriver.mbBatchReadEnable !== null
            ) {
              deviceInfo.mbBatchReadEnable = deviceDriver.mbBatchReadEnable;
            }

            let {
              totalDevicesCount,
              _siteInfo,
              _deviceList,
              _emKeystoreData,
              _mainMeterKeystoreData,
            } = await deviceService.getDeviceConfiguration(siteId, isMainMeter, isEnergyMeter);

            /**
             * Generate deviceId and convert its type to string because it will go into
             * transaction items list[string] it will take only string for that
             */
            const deviceIdCounter = deviceUtils.generateDeviceId(totalDevicesCount);
            deviceInfo.deviceId = deviceIdCounter.toString();

            //we are fetching the data from third party master controller
            deviceInfo.areaId = masterControllerData?.areaId;
            deviceInfo.regionId = masterControllerData?.regionId;
            deviceInfo.networkId = masterControllerData?.networkId;
            deviceInfo.isVirtualDevice = 0; //hardcoding
            deviceInfo.slaveId = 0; //setting up the default slaveId, it can be changed in the future based on the Discussion IoT
            deviceInfo.portNumber = 1;

            const isAreasRegionsValid = deviceUtils.validateRegionsAndAreas(deviceInfo, _siteInfo);
            if (!isAreasRegionsValid) {
              //FIXME: put this in the error block. add a message key. for ,eg here message will be Invalid regions and areas for device
              const errorIndex = errorDevices.findIndex(
                (d) => d.tempDeviceId && d.tempDeviceId === tempDeviceId
              );
              if (errorIndex !== -1) {
                errorDevices[errorIndex].message = "Invalid regions and areas for device";
              }
              continue;
            }

            //get the params from the payload
            const parameters = { parameters: deviceInfo.parameters };

            // If parameters.parameters exists and is an array, convert matchParam to string if it's an object
            if (parameters && Array.isArray(parameters.parameters)) {
              parameters.parameters = parameters.parameters.map((param) => {
                if (param && typeof param.matchParam === "object" && param.matchParam !== null) {
                  return {
                    ...param,
                    matchParam: JSON.stringify(param.matchParam),
                  };
                }
                return param;
              });
            }

            //add the params in the parameters table.
            await parameterService.addDeviceParameters(
              deviceType,
              driverType,
              deviceInfo.deviceId,
              siteId,
              parameters
            );

            //delete the parameters from deviceInfo, to not make an entry in device table
            if (deviceInfo.hasOwnProperty("parameters")) {
              delete deviceInfo.parameters;
            }

            /**
             * Get _em and _mainMeter keyStore data with appended deviceId in list
             */
            const addDeviceToDynamoKeyStore = deviceUtils.dynamoKeystoreKey(
              _emKeystoreData,
              _mainMeterKeystoreData,
              {
                deviceId: deviceInfo.deviceId,
                isMainMeter,
                siteId,
                isEnergyMeter,
              }
            );

            //create the map to add in dynamo
            const addDeviceParameters = deviceUtils.generateAddDeviceParameters(deviceInfo);

            //add device, using transaction
            await deviceService.addDevice(
              {
                deviceId: deviceInfo.deviceId,
                siteId: siteId,
              },
              addDeviceParameters,
              addDeviceToDynamoKeyStore
            );

            await deviceService.removeCachedDeviceCategories(siteId);

            // const auditPayload = {
            //   event_name: "state_create_device",
            //   user_id: userId,
            //   site_id: siteId,
            //   asset_id: deviceInfo.deviceId,
            //   req: this.req,
            //   prev_state: null,
            //   curr_state: deviceInfo,
            // };

            // auditEventLogService.emit(auditPayload);

            successDevices.push(deviceInfo);
            // Optionally, remove from errorDevices if it was previously added
            const errorIndex = errorDevices.findIndex((d) => d.name === deviceInfo.name);
            if (errorIndex !== -1) {
              errorDevices.splice(errorIndex, 1);
            }
          }
        }

        if (type === "UPDATE") {
          // UPDATE: Update existing devices
          //   params, 3rdpartyMasterController, area, region,

          for (const deviceConfig of data) {
            deviceConfig.siteId = siteId;
            deviceConfig.isVirtualDevice = 0;
            errorDevices.push(deviceConfig);

            sails.log.info(`Updating device: ${JSON.stringify(deviceConfig)}`);

            let { deviceId } = deviceConfig;
            let device = await deviceService.findOne({ deviceId });
            let isEnergyMeter = false;

            if (deviceConfig.deviceType === "em") {
              isEnergyMeter = true;
            }

            if (!device) {
              return exits.badRequest({ problems: ["Device doesn't exist"] });
            }

            let { deviceType, driverType } = device;

            //Handle parameters udpate
            //delete all parameters of the device
            await parameterservice.destroyWithoutAbbr(siteId, deviceId);

            // If parameters.parameters exists and is an array, convert matchParam to string if it's an object
            if (deviceConfig.parameters && Array.isArray(deviceConfig.parameters)) {
              const parameters = deviceConfig.parameters;
              deviceConfig.parameters = parameters.map((param) => {
                if (param && typeof param.matchParam === "object" && param.matchParam !== null) {
                  return {
                    ...param,
                    matchParam: JSON.stringify(param.matchParam),
                  };
                }
                return param;
              });
            }

            const driver = {
              parameters: deviceConfig.parameters,
              deviceType,
              driverType,
            };

            // Add or update parameters for the device
            let paramList = utils.addDeviceParameters(device.deviceId, driver, siteId);
            await parameterservice.create(paramList);

            // Remove restricted fields from the update payload
            delete deviceConfig.deviceId;
            delete deviceConfig.siteId;

            // Perform the device update
            let updatedDevice = await deviceService.getUpdatedDevices(
              deviceId,
              siteId,
              deviceConfig
            );
            if (!updatedDevice) {
              return exits.badRequest({ problems: ["Cannot update devices."] });
            }

            // Update configuration timestamp
            await dynamokeystoreservice.updateConfigTs(siteId);
            successDevices.push(updatedDevice);

            // Optionally, remove from errorDevices if it was previously added
            const errorIndex = errorDevices.findIndex((d) => d.deviceId === deviceConfig.deviceId);
            if (errorIndex !== -1) {
              errorDevices.splice(errorIndex, 1);
            }
          }
        } else {
          sails.log.warn(`Unknown operation type: ${type}`);
        }
      }

      const response = {
        successControllers: successDevices,
        errorControllers: errorDevices,
      };
      return exits.success(response);
    } catch (error) {
      sails.log.error(`Error in fetchBacnetDevice: ${error}`);

      switch (error.code) {
        case "E_NOT_FOUND": {
          return exits.notFound({ error: error.message });
        }
        default: {
          return exits.serverError(error);
        }
      }
    }
  },
};
