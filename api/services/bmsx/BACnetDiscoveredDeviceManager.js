class BACnetDiscoveredDeviceManager {
  constructor({ siteId, slaveController, discoveredData, protocol = "bacnet_to_mqtt", connectorClass = "n3uronbacnetmqtt",bmsConnectorRefId }) {
    this.siteId = siteId;
    this.slaveController = slaveController;
    this.discoveredData = discoveredData || [];
    this.protocol = protocol;
    this.connectorClass = connectorClass;
    this.bmsConnectorRefId = bmsConnectorRefId
    this.buildAddressMap(this.discoveredData);
  }

  

  buildAddressMap(devices) {
    this.AddressMap = devices.reduce((map, device) => {
      const objectAddressMap = (device.objects || []).reduce((omap, object) => {
        const propertyAddressMap = (object.properties || []).reduce((pmap, property) => {
          pmap[property.address] = property;
          return pmap;
        }, {});

        omap[object.address] = {
          ...object,
          propertyAddressMap,
        };

        return omap;
      }, {});

      map[device.address] = {
        ...device,
        objectAddressMap,
      };

      return map;
    }, {});
  }

  async syncData() {
    for (const device of this.discoveredData) {
      await this.syncDeviceData(device);
    }
  }

  async syncDeviceData(device) {
    const db = sails.getDatastore("postgres");

    await db.transaction(async (conn) => {
      const query = `
        SELECT 
          device.id as "devicePk", device.address as "deviceAddress",
          object.id as "objectPk", object.address as "objectAddress",
          properties.id as "propertyPk", properties.address as "propertyAddress"
        FROM third_party_bms_devices AS device
        LEFT JOIN third_party_bms_device_objects AS object ON device.id = object.device_ref_id
        LEFT JOIN third_party_bms_object_properties AS properties ON object.id = properties.object_ref_id
        WHERE device.address = $1 AND device.bms_connector_ref_id = $2
      `;

      const result = await db.sendNativeQuery(query, [device.address, this.bmsConnectorRefId]);
      const rawRows = result.rows;

      const existingDataMap = this.mapExistingData(rawRows);

      if (existingDataMap[device.address]) {
        await BMSDevice.update({ id: existingDataMap[device.address].devicePk })
          .set({
            name: device.name,
            description: device.description,
            systemStatus: device.systemStatus,
            location: device.location,
            vendorName: device.vendorName,
            modelName: device.modelName,
            status: 1,
          })
          .usingConnection(conn);
      } else {
        const newDevice = await BMSDevice.create({
          name: device.name,
          description: device.description,
          systemStatus: device.systemStatus,
          location: device.location,
          vendorName: device.vendorName,
          modelName: device.modelName,
          status: 1,
          address: device.address,
          bmsConnectorRefId: this.bmsConnectorRefId,
        })
          .usingConnection(conn)
          .fetch();

        existingDataMap[device.address] = {
          devicePk: newDevice.id,
          objectAddressMap: {},
        };
      }

      if (_.isEmpty(device.objects)) {
        sails.log.warn(`No objects found for device ${device.name} (${device.address})`);
        return;
      }

      for (const object of device.objects) {
        await this.syncObjectData(device.address, object, existingDataMap, conn);
      }
    });
  }

  mapExistingData(rows) {
    return rows.reduce((map, row) => {
      if (!map[row.deviceAddress]) {
        map[row.deviceAddress] = { devicePk: row.devicePk, objectAddressMap: {} };
      }
      if (row.objectAddress) {
        map[row.deviceAddress].objectAddressMap[row.objectAddress] = map[row.deviceAddress].objectAddressMap[row.objectAddress] || {
          objectPk: row.objectPk,
          propertyAddressMap: {},
        };
      }
      if (row.propertyAddress) {
        map[row.deviceAddress].objectAddressMap[row.objectAddress].propertyAddressMap[row.propertyAddress] = {
          propertyPk: row.propertyPk,
        };
      }
      return map;
    }, {});
  }

  async syncObjectData(deviceAddress, object, existingMap, conn) {
    const deviceMap = existingMap[deviceAddress];
    const existingObject = deviceMap.objectAddressMap[object.address];

    if (existingObject) {
      await BMSDeviceObject.update({ id: existingObject.objectPk })
        .set({
          name: object.name,
          description: object.description,
          eventState: object.eventState,
          outOfService: object.outOfService,
          reliability: object.reliability,
          statusFlags: object.statusFlags,
          protocolNativeUnit: object.protocolNativeUnit,
          unit: object.unit,
        })
        .usingConnection(conn);
    } else {
      const newObject = await BMSDeviceObject.create({
        name: object.name,
        description: object.description,
        eventState: object.eventState,
        outOfService: object.outOfService,
        reliability: object.reliability,
        statusFlags: object.statusFlags,
        protocolNativeUnit: object.protocolNativeUnit,
        unit: object.unit,
        address: object.address,
        deviceRefId: deviceMap.devicePk,
      })
        .usingConnection(conn)
        .fetch();

      existingMap[deviceAddress].objectAddressMap[object.address] = {
        objectPk: newObject.id,
        propertyAddressMap: {},
      };
    }

    await this.syncProperties(deviceAddress, object, existingMap, conn);
  }

  async syncProperties(deviceAddress, object, existingMap, conn) {
    const properties = object.properties || [];
    if (_.isEmpty(properties)) {
      sails.log.warn(`No properties for object ${object.name} (${object.address})`);
      return;
    }

    for (const property of properties) {
      const existingProperty = existingMap[deviceAddress]?.objectAddressMap[object.address]?.propertyAddressMap[property.address];

      if (existingProperty) {
        await BMSObjectProperty.update({ id: existingProperty.propertyPk })
          .set({
            propertyTag: property.propertyTag,
            type: property.type,
            recentValue: property.value,
            protocolNativeUnit: property.protocolNativeUnit,
            unit: property.unit,
          })
          .usingConnection(conn);
      } else {
        const newProperty = await BMSObjectProperty.create({
          propertyTag: property.propertyTag,
          type: property.type,
          recentValue: property.value,
          protocolNativeUnit: property.protocolNativeUnit,
          unit: property.unit,
          address: property.address,
          objectRefId: existingMap[deviceAddress].objectAddressMap[object.address].objectPk,
          status: 1,
        })
          .usingConnection(conn)
          .fetch();

        existingMap[deviceAddress].objectAddressMap[object.address].propertyAddressMap[property.address] = {
          propertyPk: newProperty.id,
        };
      }
    }
  }
}
module.exports = BACnetDiscoveredDeviceManager;
