const regexToServiceMapping = {
  // diagnostic Service
  // '.*\\/response\\/\\d+\\/diagnostic$': 'diagnosticService',
  // '.*\\/feedback\\/\\d+\\/cicd$': 'diagnosticService',
  // '^local\\/feedback|req\\/\\d+\\/cicd$|status$': 'diagnosticService',
  // '^feedback\\/\\d+\\/cicd$': 'diagnosticService',
  // modes service
  // '.*\\/feedback\\/\\d+\\/mode$|pidConfig$': 'modeService',
  // action service
  // '.*\\/feedback\\/\\d+\\/joulerecipe$': 'actionService',
  // '.*\\/feedback\\/\\d+\\/jouletrack$': 'actionService',
  // alert service
  // '.*\\/sendalerts\\/\\d+\\/.*$': 'alertService',
  "m2/request\\/\\d+\\/.*$": "diagnosticService",
  ".*\\/alerts\\/\\d+\\/.*$": "notificationService", // Joulerecipe alerts from controllers/server. TOPIC=siteid/alerts/deviceId/<recipe/joulerecipe>
  // recipe service
  ".*\\/feedback\\/\\d+\\/recipelogicconfig$|recipeControl$": "recipeService", // recipe updated / startStop updates etc feedback.
  // '.*\\/alert\\/\\d+\\/joulerecipe$': 'recipeService',
  // BMSX service - discovery response handling
  ".*\\/response\\/bmsx\\/connector-tags$": "bmsxService", // BMSX discovery result responses from IoT service. TOPIC=siteId/response/bmsx/connector-tags
  // socketservice
  // '^jouletrack\\/data\\/(\\w+-\\w+|\\w+)\\/calculated$': 'socketService',
  // '^jouletrack\\/data\\/update\\/public$': 'socketService'
};

// topics list to subscribe
const subscribeTopics = [
  "+/feedback/+/+",
  "+/alerts/+/+",
  "+/response/bmsx/connector-tags", // BMSX discovery result responses
  // 'feedback/+/cicd',
  // 'local/req/+/cicd',
  // 'global/status/+/diagnostics',
  // '+/response/+/diagnostic',
  // '+/sendalerts/0001/+',
  // 'jouletrack/+/+/+', //this group of topics will be used to display realtime data on jouletrack
];

const MQTT_SECRET = "abcxyz";

module.exports = {
  subscribeTopics,
  /**
   * Given the topic name, find the service this topic belong to, so as to call that services
   * eventHandler
   * @param {string} topic mqtt topic
   */
  findServiceToNotifyFromTopic: (topic) => {
    for (let eachRegex in regexToServiceMapping) {
      if (RegExp(eachRegex).test(topic)) {
        return regexToServiceMapping[eachRegex]; // return service name
      }
    }
    return undefined;
  },
  MQTT_SECRET,
};
