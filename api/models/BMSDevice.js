module.exports = {
  datastore: 'postgres',
  tableName: 'third_party_bms_devices',
  primaryKey: 'id',
  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    name: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Device name'
    },
    address: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Device address'
    },
    systemStatus: {
      type: 'string',
      columnType: 'varchar(255)',
      columnName: 'system_status',
      description: 'System status'
    },
    description: {
      type: 'string',
      columnType: 'text',
      description: 'Device description'
    },
    vendorName: {
      type: 'string',
      columnType: 'varchar(255)',
      columnName: 'vendor_name',
      description: 'Vendor name'
    },
    modelName: {
      type: 'string',
      columnType: 'varchar(255)',
      columnName: 'model_name',
      description: 'Model name'
    },
    status: {
      type: 'number',
      description: 'Device status'
    },
    location: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Device location'
    },
    bmsConnectorRefId: {
      type: 'number',
      columnName: 'bms_connector_ref_id',
      required: true,
      description: 'Foreign key to BMSConnector'
    },
    refDeviceId: {
      type: 'number',
      columnName: 'ref_device_id',
      description: 'Reference device ID'
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      autoCreatedAt: true,
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      autoUpdatedAt: true,
    }
  }
};
