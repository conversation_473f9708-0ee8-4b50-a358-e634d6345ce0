module.exports = {
  datastore: 'postgres',
  tableName: 'third_party_bms_connector',
  primaryKey: 'id',
  attributes: {
    id: {
      type: 'number',
      autoIncrement: true
    },
    siteId: {
      type: 'string',
      columnType: 'varchar(255)',
      columnName: 'site_id',
      required: true,
      description: 'Site ID'
    },
    protocol: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Protocol type (e.g., BACnet)'
    },
    bmsConnectorClass: {
      type: 'string',
      columnType: 'varchar(255)',
      columnName: 'bms_connector_class',
      description: 'BMS connector class'
    },
    status: {
      type: 'number',
      description: 'Status of the connector'
    },
    lastSyncTs: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      columnName: 'last_sync_ts',
      description: 'Last synchronization timestamp'
    },
    version: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Version of the connector'
    },
    slaveControllerId: {
      type: 'number',
      columnName: 'slave_controller_id',
      description: 'Reference to slave controller ID in DynamoDB'
    },
    discoveryStatus: {
      type: 'number',
      columnName: 'discovery_status',
      defaultsTo: 0,
      description: 'Discovery status: 0=idle, 1=discovering, 2=completed, 3=failed'
    },
    discoveryRequestId: {
      type: 'string',
      columnType: 'varchar(255)',
      columnName: 'discovery_request_id',
      allowNull: true,
      description: 'Current discovery request ID (UUID)'
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      autoCreatedAt: true
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      autoUpdatedAt: true
    }
  }
};
