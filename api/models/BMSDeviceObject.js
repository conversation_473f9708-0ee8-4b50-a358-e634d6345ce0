module.exports = {
  datastore: 'postgres',
  tableName: 'third_party_bms_device_objects',
  primaryKey: 'id',
  attributes: {
    id: {
      type: 'number',
      autoIncrement: true,
    },
    name: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Object name',
      allowNull: true
    },
    description: {
      type: 'string',
      columnType: 'text',
      description: 'Object description',
      allowNull: true
    },
    address: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Object address',
      required: true
    },
    status: {
      type: 'number',
      description: 'Object status',
      defaultsTo: 1
    },
    eventState: {
      type: 'string',
      columnType: 'varchar(255)',
      columnName: 'event_state',
      description: 'Event state',
      allowNull: true
    },
    outOfService: {
      type: 'string',
      columnType: 'varchar(255)',
      columnName: 'out_of_service',
      description: 'Out of service status',
      allowNull: true
    },
    reliability: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Reliability status',
      allowNull: true
    },
    statusFlags: {
      type: 'json',
      columnName: 'status_flags',
      description: 'Status flags as JSON'
    },
    protocolNativeUnit: {
      type: 'string',
      columnType: 'varchar(255)',
      columnName: 'protocol_native_unit',
      description: 'Protocol native unit',
      allowNull: true
    },
    unit: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Unit of measurement',
      allowNull: true
    },
    deviceRefId: {
      type: 'number',
      columnName: 'device_ref_id',
      required: true,
      description: 'Foreign key to BMSDevice'
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      autoCreatedAt: true,
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      autoUpdatedAt: true,
    }
  }
};
