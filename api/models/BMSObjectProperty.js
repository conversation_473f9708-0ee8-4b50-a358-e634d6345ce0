module.exports = {
  datastore: 'postgres',
  tableName: 'third_party_bms_object_properties',
  primaryKey: 'id',
  attributes: {
    id: {
      type: 'number',
      autoIncrement: true
    },
    propertyTag: {
      type: 'string',
      columnType: 'varchar(255)',
      columnName: 'property_tag',
      description: 'Property tag'
    },
    type: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Property type'
    },
    address: {
      type: 'string',
      columnType: 'varchar(255)',
      description: 'Property address'
    },
    status: {
      type: 'number',
      description: 'Property status',
      defaultsTo: 1
    },
    protocolNativeUnit: {
      type: 'string',
      columnType: 'varchar(255)',
      columnName: 'protocol_native_unit',
      description: 'Protocol native unit',
      allowNull: true
    },
    recentValue: {
      type: 'string',
      columnType: 'varchar(255)',
      columnName: 'recent_value',
      description: 'Recent value of the property',
      allowNull: true
    },
    objectRefId: {
      type: 'number',
      columnName: 'object_ref_id',
      required: true,
      description: 'Foreign key to BMSDeviceObject'
    },
    createdAt: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      autoCreatedAt: true,
    },
    updatedAt: {
      type: 'ref',
      columnType: 'timestamp with time zone',
      autoUpdatedAt: true,
    }
  }
};
